// 元素炼金术2048 - 特效系统

// 粒子类
class Particle {
    constructor(x, y, type, velocity = { x: 0, y: 0 }) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.velocity = velocity;
        this.life = 1.0;
        this.maxLife = 1.0;
        this.size = Math.random() * 4 + 2;
        this.color = this.getColorByType(type);
        this.alpha = 1.0;
    }

    getColorByType(type) {
        const colors = {
            fire: ['#ff4757', '#ff3838', '#ff6b6b'],
            water: ['#3742fa', '#2f3542', '#74b9ff'],
            earth: ['#8b4513', '#a0522d', '#d63031'],
            air: ['#f1f2f6', '#ddd', '#b2bec3'],
            steam: ['#74b9ff', '#a29bfe', '#81ecec'],
            lava: ['#fd79a8', '#e84393', '#ff7675'],
            mud: ['#6c5ce7', '#a29bfe', '#8b4513'],
            plasma: ['#00cec9', '#55efc4', '#74b9ff']
        };
        
        const typeColors = colors[type] || colors.fire;
        return typeColors[Math.floor(Math.random() * typeColors.length)];
    }

    update(deltaTime) {
        this.x += this.velocity.x * deltaTime;
        this.y += this.velocity.y * deltaTime;
        this.life -= deltaTime / 1000; // 1秒生命周期
        this.alpha = this.life / this.maxLife;
        
        // 重力效果
        this.velocity.y += 0.1;
        
        // 阻力
        this.velocity.x *= 0.99;
        this.velocity.y *= 0.99;
        
        return this.life > 0;
    }

    render(ctx) {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 特效管理器
class EffectsManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.lastTime = 0;
        this.isRunning = false;
    }

    start() {
        this.isRunning = true;
        this.animate();
    }

    stop() {
        this.isRunning = false;
    }

    animate(currentTime = 0) {
        if (!this.isRunning) return;

        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        this.update(deltaTime);
        this.render();

        requestAnimationFrame((time) => this.animate(time));
    }

    update(deltaTime) {
        // 更新所有粒子
        this.particles = this.particles.filter(particle => particle.update(deltaTime));
    }

    render() {
        // 清除粒子层（不影响游戏主画面）
        this.ctx.save();
        this.ctx.globalCompositeOperation = 'destination-over';
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();

        // 渲染所有粒子
        this.particles.forEach(particle => particle.render(this.ctx));
    }

    // 创建元素出现特效
    createElementAppearEffect(x, y, elementType) {
        const particleCount = 15;
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount;
            const speed = Math.random() * 50 + 25;
            const velocity = {
                x: Math.cos(angle) * speed,
                y: Math.sin(angle) * speed
            };
            
            this.particles.push(new Particle(x, y, elementType, velocity));
        }
    }

    // 创建元素合成特效
    createSynthesisEffect(x, y, fromType, toType) {
        // 爆炸效果
        const explosionCount = 20;
        for (let i = 0; i < explosionCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 80 + 40;
            const velocity = {
                x: Math.cos(angle) * speed,
                y: Math.sin(angle) * speed
            };
            
            this.particles.push(new Particle(x, y, fromType, velocity));
        }

        // 延迟创建新元素特效
        setTimeout(() => {
            this.createElementAppearEffect(x, y, toType);
        }, 200);
    }

    // 创建连锁反应特效
    createChainReactionEffect(positions, elementType) {
        positions.forEach((pos, index) => {
            setTimeout(() => {
                this.createElementAppearEffect(pos.x, pos.y, elementType);
                
                // 连接线效果
                if (index > 0) {
                    this.createLightningEffect(positions[index - 1], pos);
                }
            }, index * 100);
        });
    }

    // 创建闪电连接特效
    createLightningEffect(from, to) {
        const steps = 10;
        const stepX = (to.x - from.x) / steps;
        const stepY = (to.y - from.y) / steps;

        for (let i = 0; i <= steps; i++) {
            const x = from.x + stepX * i + (Math.random() - 0.5) * 20;
            const y = from.y + stepY * i + (Math.random() - 0.5) * 20;
            
            const velocity = {
                x: (Math.random() - 0.5) * 10,
                y: (Math.random() - 0.5) * 10
            };
            
            this.particles.push(new Particle(x, y, 'plasma', velocity));
        }
    }

    // 创建混沌风暴特效
    createChaosStormEffect(centerX, centerY, radius) {
        const stormCount = 50;
        for (let i = 0; i < stormCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * radius;
            const x = centerX + Math.cos(angle) * distance;
            const y = centerY + Math.sin(angle) * distance;
            
            const velocity = {
                x: Math.cos(angle + Math.PI / 2) * 30,
                y: Math.sin(angle + Math.PI / 2) * 30
            };
            
            const types = ['fire', 'water', 'earth', 'air'];
            const randomType = types[Math.floor(Math.random() * types.length)];
            
            this.particles.push(new Particle(x, y, randomType, velocity));
        }
    }

    // 创建元素光环特效
    createAuraEffect(x, y, elementType, intensity = 1) {
        const auraCount = Math.floor(8 * intensity);
        for (let i = 0; i < auraCount; i++) {
            const angle = (Math.PI * 2 * i) / auraCount;
            const radius = 30 + Math.random() * 20;
            const particleX = x + Math.cos(angle) * radius;
            const particleY = y + Math.sin(angle) * radius;
            
            const velocity = {
                x: Math.cos(angle) * 5,
                y: Math.sin(angle) * 5
            };
            
            this.particles.push(new Particle(particleX, particleY, elementType, velocity));
        }
    }

    // 创建屏幕震动效果
    createScreenShake(intensity = 1, duration = 500) {
        const canvas = this.canvas;
        const originalTransform = canvas.style.transform;
        
        const startTime = Date.now();
        const shake = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < duration) {
                const progress = elapsed / duration;
                const currentIntensity = intensity * (1 - progress);
                
                const offsetX = (Math.random() - 0.5) * currentIntensity * 10;
                const offsetY = (Math.random() - 0.5) * currentIntensity * 10;
                
                canvas.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
                
                requestAnimationFrame(shake);
            } else {
                canvas.style.transform = originalTransform;
            }
        };
        
        shake();
    }

    // 清除所有特效
    clearAllEffects() {
        this.particles = [];
    }

    // 获取粒子数量（用于性能监控）
    getParticleCount() {
        return this.particles.length;
    }
}

// 视觉反馈管理器
class VisualFeedbackManager {
    constructor() {
        this.feedbackQueue = [];
    }

    // 显示分数增加动画
    showScoreIncrease(element, points, x, y) {
        const scoreElement = document.createElement('div');
        scoreElement.className = 'score-popup';
        scoreElement.textContent = `+${points}`;
        scoreElement.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            color: #4ecdc4;
            font-size: 1.5rem;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            animation: scorePopup 1s ease-out forwards;
        `;

        // 添加动画样式
        if (!document.querySelector('#score-popup-style')) {
            const style = document.createElement('style');
            style.id = 'score-popup-style';
            style.textContent = `
                @keyframes scorePopup {
                    0% {
                        transform: translateY(0) scale(1);
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-50px) scale(1.2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(scoreElement);

        setTimeout(() => {
            if (scoreElement.parentNode) {
                scoreElement.parentNode.removeChild(scoreElement);
            }
        }, 1000);
    }

    // 显示新元素发现提示
    showElementDiscovery(elementName, elementSymbol) {
        const notification = document.createElement('div');
        notification.className = 'discovery-notification';
        notification.innerHTML = `
            <div class="discovery-content">
                <div class="discovery-symbol">${elementSymbol}</div>
                <div class="discovery-text">发现新元素: ${elementName}</div>
            </div>
        `;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            animation: slideInRight 0.5s ease-out;
        `;

        // 添加动画样式
        if (!document.querySelector('#discovery-style')) {
            const style = document.createElement('style');
            style.id = 'discovery-style';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .discovery-content {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .discovery-symbol {
                    font-size: 1.5rem;
                }
                .discovery-text {
                    font-weight: bold;
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.5s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 500);
        }, 3000);
    }

    // 显示混沌事件提示
    showChaosEvent(eventName, description) {
        const eventElement = document.getElementById('event-log');
        if (eventElement) {
            const eventItem = document.createElement('div');
            eventItem.className = 'event-item chaos-event';
            eventItem.textContent = `${eventName}: ${description}`;
            eventItem.style.cssText = `
                background: rgba(255, 107, 107, 0.2);
                border-left: 3px solid #ff6b6b;
                animation: eventPulse 0.5s ease-out;
            `;

            eventElement.insertBefore(eventItem, eventElement.firstChild);

            // 限制事件日志数量
            while (eventElement.children.length > 5) {
                eventElement.removeChild(eventElement.lastChild);
            }
        }
    }
}

// 导出供其他模块使用
window.Particle = Particle;
window.EffectsManager = EffectsManager;
window.VisualFeedbackManager = VisualFeedbackManager;
