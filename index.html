<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元素炼金术2048</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <div class="game-container">
        <!-- 游戏标题和信息栏 -->
        <header class="game-header">
            <h1 class="game-title">元素炼金术2048</h1>
            <div class="game-info">
                <div class="score-container">
                    <div class="score-box">
                        <div class="score-label">分数</div>
                        <div class="score-value" id="score">0</div>
                    </div>
                    <div class="score-box">
                        <div class="score-label">最高分</div>
                        <div class="score-value" id="best-score">0</div>
                    </div>
                </div>
                <div class="game-controls">
                    <button class="btn btn-restart" id="restart-btn">重新开始</button>
                    <button class="btn btn-help" id="help-btn">帮助</button>
                </div>
            </div>
        </header>

        <!-- 游戏主体区域 -->
        <main class="game-main">
            <!-- 游戏画布 -->
            <div class="game-board-container">
                <canvas id="game-canvas" width="400" height="400"></canvas>
                <div class="game-overlay" id="game-overlay">
                    <div class="overlay-content">
                        <h2 id="overlay-title">游戏结束</h2>
                        <p id="overlay-message">尝试创造更高级的元素！</p>
                        <button class="btn btn-primary" id="overlay-btn">再试一次</button>
                    </div>
                </div>
            </div>

            <!-- 侧边信息面板 -->
            <aside class="info-panel">
                <!-- 当前元素信息 -->
                <div class="element-info">
                    <h3>元素信息</h3>
                    <div class="current-element" id="current-element">
                        <div class="element-preview"></div>
                        <div class="element-details">
                            <div class="element-name">选择一个元素</div>
                            <div class="element-properties">
                                <div class="property">
                                    <span class="property-label">温度:</span>
                                    <span class="property-value" id="element-temperature">--</span>
                                </div>
                                <div class="property">
                                    <span class="property-label">稳定性:</span>
                                    <span class="property-value" id="element-stability">--</span>
                                </div>
                                <div class="property">
                                    <span class="property-label">能量:</span>
                                    <span class="property-value" id="element-energy">--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合成指南 -->
                <div class="synthesis-guide">
                    <h3>合成指南</h3>
                    <div class="synthesis-list" id="synthesis-list">
                        <div class="synthesis-item">
                            <span class="synthesis-formula">火 + 水 = 蒸汽</span>
                        </div>
                        <div class="synthesis-item">
                            <span class="synthesis-formula">火 + 土 = 岩浆</span>
                        </div>
                        <div class="synthesis-item">
                            <span class="synthesis-formula">水 + 土 = 泥土</span>
                        </div>
                        <div class="synthesis-item">
                            <span class="synthesis-formula">火 + 气 = 等离子</span>
                        </div>
                    </div>
                </div>

                <!-- 混沌事件提示 -->
                <div class="chaos-events">
                    <h3>混沌事件</h3>
                    <div class="event-log" id="event-log">
                        <div class="event-item">游戏开始...</div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 帮助模态框 -->
        <div class="modal" id="help-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>游戏说明</h2>
                    <button class="modal-close" id="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <h3>基础玩法</h3>
                    <p>使用方向键或滑动手势移动元素方块，相同元素碰撞时会合成新元素。</p>
                    
                    <h3>元素系统</h3>
                    <ul>
                        <li><strong>火元素</strong>：高温、不稳定，红色光芒</li>
                        <li><strong>水元素</strong>：低温、稳定，蓝色波纹</li>
                        <li><strong>土元素</strong>：中温、稳定，棕色粒子</li>
                        <li><strong>气元素</strong>：中温、不稳定，白色气流</li>
                    </ul>

                    <h3>特殊机制</h3>
                    <p><strong>涌现效应</strong>：某些合成会触发连锁反应</p>
                    <p><strong>混沌现象</strong>：随机事件会改变游戏状态</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="scripts/elements.js"></script>
    <script src="scripts/effects.js"></script>
    <script src="scripts/audio.js"></script>
    <script src="scripts/game.js"></script>
</body>
</html>
