// 元素炼金术2048 - 元素系统

// 元素类型定义
const ElementTypes = {
    FIRE: 'fire',
    WATER: 'water',
    EARTH: 'earth',
    AIR: 'air',
    STEAM: 'steam',
    LAVA: 'lava',
    MUD: 'mud',
    PLASMA: 'plasma',
    ICE: 'ice',
    CRYSTAL: 'crystal',
    METAL: 'metal',
    VOID: 'void'
};

// 元素属性配置
const ElementConfig = {
    [ElementTypes.FIRE]: {
        name: '火',
        symbol: '🔥',
        color: '#ff4757',
        temperature: 85,
        stability: 30,
        energy: 80,
        level: 1,
        description: '炽热的火焰元素，充满破坏性能量'
    },
    [ElementTypes.WATER]: {
        name: '水',
        symbol: '💧',
        color: '#3742fa',
        temperature: 25,
        stability: 80,
        energy: 40,
        level: 1,
        description: '流动的水元素，具有净化和治愈能力'
    },
    [ElementTypes.EARTH]: {
        name: '土',
        symbol: '🌍',
        color: '#8b4513',
        temperature: 45,
        stability: 90,
        energy: 60,
        level: 1,
        description: '坚实的土地元素，象征稳定与力量'
    },
    [ElementTypes.AIR]: {
        name: '气',
        symbol: '💨',
        color: '#f1f2f6',
        temperature: 35,
        stability: 20,
        energy: 70,
        level: 1,
        description: '自由的风元素，代表速度与变化'
    },
    [ElementTypes.STEAM]: {
        name: '蒸汽',
        symbol: '☁️',
        color: '#74b9ff',
        temperature: 65,
        stability: 40,
        energy: 90,
        level: 2,
        description: '火与水的结合，蕴含巨大能量'
    },
    [ElementTypes.LAVA]: {
        name: '岩浆',
        symbol: '🌋',
        color: '#fd79a8',
        temperature: 95,
        stability: 50,
        energy: 120,
        level: 2,
        description: '火与土的融合，毁灭与创造并存'
    },
    [ElementTypes.MUD]: {
        name: '泥土',
        symbol: '🟫',
        color: '#6c5ce7',
        temperature: 30,
        stability: 85,
        energy: 50,
        level: 2,
        description: '水与土的混合，生命的摇篮'
    },
    [ElementTypes.PLASMA]: {
        name: '等离子',
        symbol: '⚡',
        color: '#00cec9',
        temperature: 100,
        stability: 10,
        energy: 150,
        level: 2,
        description: '火与气的极致形态，纯粹的能量'
    },
    [ElementTypes.ICE]: {
        name: '冰',
        symbol: '❄️',
        color: '#81ecec',
        temperature: 5,
        stability: 70,
        energy: 30,
        level: 2,
        description: '水的固态形式，冰冷而坚硬'
    },
    [ElementTypes.CRYSTAL]: {
        name: '水晶',
        symbol: '💎',
        color: '#a29bfe',
        temperature: 20,
        stability: 95,
        energy: 80,
        level: 3,
        description: '土的精华结晶，蕴含神秘力量'
    },
    [ElementTypes.METAL]: {
        name: '金属',
        symbol: '⚙️',
        color: '#636e72',
        temperature: 40,
        stability: 100,
        energy: 70,
        level: 3,
        description: '土与火的完美结合，坚不可摧'
    },
    [ElementTypes.VOID]: {
        name: '虚空',
        symbol: '🕳️',
        color: '#2d3436',
        temperature: 0,
        stability: 0,
        energy: 200,
        level: 4,
        description: '所有元素的终极形态，吞噬一切'
    }
};

// 元素合成规则
const SynthesisRules = {
    // 基础合成
    [`${ElementTypes.FIRE}_${ElementTypes.WATER}`]: ElementTypes.STEAM,
    [`${ElementTypes.WATER}_${ElementTypes.FIRE}`]: ElementTypes.STEAM,
    
    [`${ElementTypes.FIRE}_${ElementTypes.EARTH}`]: ElementTypes.LAVA,
    [`${ElementTypes.EARTH}_${ElementTypes.FIRE}`]: ElementTypes.LAVA,
    
    [`${ElementTypes.WATER}_${ElementTypes.EARTH}`]: ElementTypes.MUD,
    [`${ElementTypes.EARTH}_${ElementTypes.WATER}`]: ElementTypes.MUD,
    
    [`${ElementTypes.FIRE}_${ElementTypes.AIR}`]: ElementTypes.PLASMA,
    [`${ElementTypes.AIR}_${ElementTypes.FIRE}`]: ElementTypes.PLASMA,
    
    // 高级合成
    [`${ElementTypes.STEAM}_${ElementTypes.STEAM}`]: ElementTypes.ICE,
    [`${ElementTypes.LAVA}_${ElementTypes.LAVA}`]: ElementTypes.CRYSTAL,
    [`${ElementTypes.MUD}_${ElementTypes.MUD}`]: ElementTypes.METAL,
    [`${ElementTypes.PLASMA}_${ElementTypes.PLASMA}`]: ElementTypes.VOID,
    
    // 特殊合成
    [`${ElementTypes.ICE}_${ElementTypes.LAVA}`]: ElementTypes.STEAM,
    [`${ElementTypes.LAVA}_${ElementTypes.ICE}`]: ElementTypes.STEAM,
    
    [`${ElementTypes.CRYSTAL}_${ElementTypes.PLASMA}`]: ElementTypes.VOID,
    [`${ElementTypes.PLASMA}_${ElementTypes.CRYSTAL}`]: ElementTypes.VOID
};

// 元素类
class Element {
    constructor(type, value = 2) {
        this.type = type;
        this.value = value;
        this.config = ElementConfig[type];
        this.id = Math.random().toString(36).substr(2, 9);
        this.age = 0; // 元素存在时间
        this.instability = 0; // 不稳定度
    }

    // 获取元素显示信息
    getDisplayInfo() {
        return {
            symbol: this.config.symbol,
            name: this.config.name,
            color: this.config.color,
            value: this.value,
            temperature: this.config.temperature,
            stability: this.config.stability,
            energy: this.config.energy,
            level: this.config.level,
            description: this.config.description
        };
    }

    // 检查是否可以与另一个元素合成
    canSynthesizeWith(otherElement) {
        if (this.type === otherElement.type && this.value === otherElement.value) {
            return true; // 相同元素可以合成
        }
        
        const key1 = `${this.type}_${otherElement.type}`;
        const key2 = `${otherElement.type}_${this.type}`;
        
        return SynthesisRules[key1] || SynthesisRules[key2];
    }

    // 与另一个元素合成
    synthesizeWith(otherElement) {
        if (this.type === otherElement.type && this.value === otherElement.value) {
            // 相同元素合成，数值翻倍
            return new Element(this.type, this.value * 2);
        }
        
        const key1 = `${this.type}_${otherElement.type}`;
        const key2 = `${otherElement.type}_${this.type}`;
        
        const resultType = SynthesisRules[key1] || SynthesisRules[key2];
        
        if (resultType) {
            const newValue = Math.max(this.value, otherElement.value);
            return new Element(resultType, newValue);
        }
        
        return null;
    }

    // 更新元素状态（用于混沌效应）
    update() {
        this.age++;
        
        // 不稳定元素可能发生变化
        if (this.config.stability < 50) {
            this.instability += (100 - this.config.stability) / 100;
            
            // 达到临界不稳定度时可能发生变异
            if (this.instability > 10 && Math.random() < 0.01) {
                return this.mutate();
            }
        }
        
        return null; // 无变化
    }

    // 元素变异（混沌现象）
    mutate() {
        const possibleMutations = Object.keys(ElementTypes).filter(type => 
            type !== this.type && ElementConfig[type].level <= this.config.level + 1
        );
        
        if (possibleMutations.length > 0) {
            const newType = possibleMutations[Math.floor(Math.random() * possibleMutations.length)];
            return new Element(newType, this.value);
        }
        
        return null;
    }

    // 获取CSS类名
    getCSSClass() {
        return `element-${this.type}`;
    }
}

// 元素管理器
class ElementManager {
    constructor() {
        this.discoveredElements = new Set([
            ElementTypes.FIRE,
            ElementTypes.WATER,
            ElementTypes.EARTH,
            ElementTypes.AIR
        ]);
    }

    // 创建随机基础元素
    createRandomElement() {
        const baseElements = [ElementTypes.FIRE, ElementTypes.WATER, ElementTypes.EARTH, ElementTypes.AIR];
        const randomType = baseElements[Math.floor(Math.random() * baseElements.length)];
        return new Element(randomType, 2);
    }

    // 发现新元素
    discoverElement(elementType) {
        if (!this.discoveredElements.has(elementType)) {
            this.discoveredElements.add(elementType);
            return true; // 新发现
        }
        return false; // 已知元素
    }

    // 获取所有已发现的元素
    getDiscoveredElements() {
        return Array.from(this.discoveredElements).map(type => ElementConfig[type]);
    }

    // 获取合成提示
    getSynthesisHints() {
        const hints = [];
        for (const [key, result] of Object.entries(SynthesisRules)) {
            const [type1, type2] = key.split('_');
            if (this.discoveredElements.has(type1) && this.discoveredElements.has(type2)) {
                const config1 = ElementConfig[type1];
                const config2 = ElementConfig[type2];
                const resultConfig = ElementConfig[result];
                
                hints.push({
                    formula: `${config1.name} + ${config2.name} = ${resultConfig.name}`,
                    discovered: this.discoveredElements.has(result)
                });
            }
        }
        return hints;
    }
}

// 导出供其他模块使用
window.ElementTypes = ElementTypes;
window.ElementConfig = ElementConfig;
window.Element = Element;
window.ElementManager = ElementManager;
