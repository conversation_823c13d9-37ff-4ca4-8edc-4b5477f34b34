/* 元素炼金术2048 - 主样式文件 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 游戏容器 */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 游戏标题栏 */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-title {
    font-size: 3rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: 20px;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.score-container {
    display: flex;
    gap: 20px;
}

.score-box {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.score-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.score-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4ecdc4;
}

.game-controls {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-restart {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.btn-help {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.btn-primary {
    background: linear-gradient(45deg, #45b7d1, #96ceb4);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 游戏主体区域 */
.game-main {
    display: flex;
    gap: 30px;
    flex: 1;
    align-items: flex-start;
}

/* 游戏画布容器 */
.game-board-container {
    position: relative;
    flex: 0 0 400px;
}

#game-canvas {
    border-radius: 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

/* 游戏覆盖层 */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.overlay-content {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#overlay-title {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #ff6b6b;
}

#overlay-message {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* 信息面板 */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-panel > div {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-panel h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #4ecdc4;
    border-bottom: 2px solid rgba(78, 205, 196, 0.3);
    padding-bottom: 8px;
}

/* 元素信息 */
.current-element {
    display: flex;
    gap: 15px;
    align-items: center;
}

.element-preview {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    background: linear-gradient(45deg, #333, #555);
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.element-details {
    flex: 1;
}

.element-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #ffffff;
}

.element-properties {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.property {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
}

.property-label {
    opacity: 0.8;
}

.property-value {
    color: #4ecdc4;
    font-weight: 500;
}

/* 合成指南 */
.synthesis-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.synthesis-item {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #4ecdc4;
}

.synthesis-formula {
    font-size: 0.9rem;
    font-family: monospace;
}

/* 混沌事件 */
.event-log {
    max-height: 150px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.event-item {
    padding: 6px 10px;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 6px;
    font-size: 0.85rem;
    border-left: 2px solid #ff6b6b;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: rgba(26, 26, 46, 0.95);
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 20px;
}

.modal-body h3 {
    color: #4ecdc4;
    margin-bottom: 10px;
    margin-top: 20px;
}

.modal-body h3:first-child {
    margin-top: 0;
}

.modal-body p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.modal-body ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.modal-body li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
    }
    
    .game-board-container {
        flex: none;
        align-self: center;
    }
    
    #game-canvas {
        width: 300px;
        height: 300px;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .game-title {
        font-size: 2rem;
    }
}
