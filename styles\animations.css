/* 元素炼金术2048 - 动画样式文件 */

/* 关键帧动画定义 */

/* 标题渐变动画 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 元素出现动画 */
@keyframes elementAppear {
    0% {
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 元素合成动画 */
@keyframes elementMerge {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.3);
        filter: brightness(1.5) saturate(1.5);
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

/* 元素移动动画 */
@keyframes elementMove {
    0% {
        transform: translateX(0) translateY(0);
    }
    100% {
        transform: translateX(var(--move-x, 0)) translateY(var(--move-y, 0));
    }
}

/* 粒子爆炸动画 */
@keyframes particleExplosion {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(2) rotate(360deg);
        opacity: 0;
    }
}

/* 光环脉动动画 */
@keyframes auraPulse {
    0% {
        box-shadow: 0 0 5px currentColor;
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 5px currentColor;
        transform: scale(1);
    }
}

/* 混沌扭曲动画 */
@keyframes chaosDistortion {
    0% {
        transform: skew(0deg, 0deg) scale(1);
        filter: hue-rotate(0deg);
    }
    25% {
        transform: skew(2deg, 1deg) scale(1.02);
        filter: hue-rotate(90deg);
    }
    50% {
        transform: skew(-1deg, 2deg) scale(0.98);
        filter: hue-rotate(180deg);
    }
    75% {
        transform: skew(1deg, -1deg) scale(1.01);
        filter: hue-rotate(270deg);
    }
    100% {
        transform: skew(0deg, 0deg) scale(1);
        filter: hue-rotate(360deg);
    }
}

/* 屏幕震动动画 */
@keyframes screenShake {
    0%, 100% {
        transform: translateX(0);
    }
    10% {
        transform: translateX(-2px);
    }
    20% {
        transform: translateX(2px);
    }
    30% {
        transform: translateX(-2px);
    }
    40% {
        transform: translateX(2px);
    }
    50% {
        transform: translateX(-1px);
    }
    60% {
        transform: translateX(1px);
    }
    70% {
        transform: translateX(-1px);
    }
    80% {
        transform: translateX(1px);
    }
    90% {
        transform: translateX(-1px);
    }
}

/* 元素特效类 */

/* 火元素特效 */
.element-fire {
    background: linear-gradient(45deg, #ff4757, #ff3838);
    color: #ffffff;
    animation: auraPulse 2s ease-in-out infinite;
    box-shadow: 0 0 15px #ff4757;
}

.element-fire::before {
    content: '🔥';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
    animation: particleExplosion 3s ease-in-out infinite;
}

/* 水元素特效 */
.element-water {
    background: linear-gradient(45deg, #3742fa, #2f3542);
    color: #ffffff;
    animation: auraPulse 2.5s ease-in-out infinite;
    box-shadow: 0 0 15px #3742fa;
}

.element-water::before {
    content: '💧';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
    animation: elementMove 4s ease-in-out infinite;
}

/* 土元素特效 */
.element-earth {
    background: linear-gradient(45deg, #8b4513, #a0522d);
    color: #ffffff;
    animation: auraPulse 3s ease-in-out infinite;
    box-shadow: 0 0 15px #8b4513;
}

.element-earth::before {
    content: '🌍';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
}

/* 气元素特效 */
.element-air {
    background: linear-gradient(45deg, #f1f2f6, #ddd);
    color: #2f3542;
    animation: auraPulse 1.5s ease-in-out infinite;
    box-shadow: 0 0 15px #f1f2f6;
}

.element-air::before {
    content: '💨';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2em;
    animation: chaosDistortion 5s ease-in-out infinite;
}

/* 高级元素特效 */
.element-steam {
    background: linear-gradient(45deg, #74b9ff, #a29bfe);
    animation: auraPulse 2s ease-in-out infinite, chaosDistortion 8s ease-in-out infinite;
    box-shadow: 0 0 20px #74b9ff;
}

.element-lava {
    background: linear-gradient(45deg, #fd79a8, #e84393);
    animation: auraPulse 1.5s ease-in-out infinite, particleExplosion 4s ease-in-out infinite;
    box-shadow: 0 0 25px #fd79a8;
}

.element-mud {
    background: linear-gradient(45deg, #6c5ce7, #a29bfe);
    animation: auraPulse 3.5s ease-in-out infinite;
    box-shadow: 0 0 15px #6c5ce7;
}

.element-plasma {
    background: linear-gradient(45deg, #00cec9, #55efc4);
    animation: auraPulse 1s ease-in-out infinite, chaosDistortion 3s ease-in-out infinite;
    box-shadow: 0 0 30px #00cec9;
}

/* 交互动画类 */
.element-appear {
    animation: elementAppear 0.5s ease-out;
}

.element-merge {
    animation: elementMerge 0.6s ease-in-out;
}

.element-move {
    animation: elementMove 0.3s ease-out;
}

.screen-shake {
    animation: screenShake 0.5s ease-in-out;
}

.chaos-active {
    animation: chaosDistortion 2s ease-in-out infinite;
}

/* 粒子效果容器 */
.particle-container {
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    pointer-events: none;
    animation: particleExplosion 1s ease-out forwards;
}

.particle-fire {
    background: radial-gradient(circle, #ff4757, #ff3838);
    box-shadow: 0 0 6px #ff4757;
}

.particle-water {
    background: radial-gradient(circle, #3742fa, #2f3542);
    box-shadow: 0 0 6px #3742fa;
}

.particle-earth {
    background: radial-gradient(circle, #8b4513, #a0522d);
    box-shadow: 0 0 6px #8b4513;
}

.particle-air {
    background: radial-gradient(circle, #f1f2f6, #ddd);
    box-shadow: 0 0 6px #f1f2f6;
}

/* 连锁反应效果 */
.chain-reaction {
    position: relative;
}

.chain-reaction::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid #ffeaa7;
    border-radius: inherit;
    animation: auraPulse 0.5s ease-in-out 3;
}

/* 临界点效果 */
.critical-point {
    animation: screenShake 0.3s ease-in-out, auraPulse 0.5s ease-in-out infinite;
    filter: brightness(1.5) saturate(1.5);
}

/* 混沌事件视觉效果 */
.chaos-event-active {
    filter: hue-rotate(180deg) saturate(1.5);
    animation: chaosDistortion 1s ease-in-out infinite;
}

/* 过渡效果 */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
    animation: elementAppear 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 悬停效果 */
.interactive-element:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
    transition: all 0.2s ease;
}

/* 加载动画 */
@keyframes loadingPulse {
    0%, 100% {
        opacity: 0.4;
    }
    50% {
        opacity: 1;
    }
}

.loading {
    animation: loadingPulse 1.5s ease-in-out infinite;
}
