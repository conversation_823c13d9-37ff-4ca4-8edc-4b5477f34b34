// 元素炼金术2048 - 主游戏逻辑

class Game {
    constructor() {
        // 游戏状态
        this.isRunning = false;
        this.isPaused = false;
        this.gameOver = false;
        this.score = 0;
        this.bestScore = this.loadBestScore();
        
        // 游戏网格
        this.gridSize = 4;
        this.grid = [];
        this.previousGrid = [];
        
        // 游戏元素
        this.elementManager = new ElementManager();
        this.effectsManager = null;
        this.audioManager = new AudioManager();
        this.visualFeedback = new VisualFeedbackManager();
        
        // Canvas相关
        this.canvas = null;
        this.ctx = null;
        this.cellSize = 90;
        this.cellPadding = 10;
        
        // 输入处理
        this.inputHandler = null;
        this.isAnimating = false;
        
        // 混沌系统
        this.chaosLevel = 0;
        this.chaosEvents = [];
        this.lastChaosTime = 0;
        
        // 初始化游戏
        this.init();
    }

    init() {
        this.setupCanvas();
        this.setupInputHandlers();
        this.setupUI();
        this.initGrid();
        this.addRandomElement();
        this.addRandomElement();
        this.updateDisplay();
        this.isRunning = true;
        
        // 启动特效系统
        this.effectsManager = new EffectsManager(this.canvas);
        this.effectsManager.start();
        
        console.log('元素炼金术2048 游戏初始化完成');
    }

    setupCanvas() {
        this.canvas = document.getElementById('game-canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 设置Canvas尺寸
        const totalSize = this.gridSize * this.cellSize + (this.gridSize + 1) * this.cellPadding;
        this.canvas.width = totalSize;
        this.canvas.height = totalSize;
        
        // 设置高DPI支持
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }

    setupInputHandlers() {
        // 键盘输入
        document.addEventListener('keydown', (e) => {
            if (!this.isRunning || this.isAnimating || this.gameOver) return;
            
            let moved = false;
            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    moved = this.move('up');
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    moved = this.move('down');
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    moved = this.move('left');
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    moved = this.move('right');
                    break;
            }
            
            if (moved) {
                this.audioManager.playSound('elementMove');
                this.afterMove();
            }
        });

        // 触摸输入
        let touchStartX = 0;
        let touchStartY = 0;
        
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });
        
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (!this.isRunning || this.isAnimating || this.gameOver) return;
            
            const touch = e.changedTouches[0];
            const deltaX = touch.clientX - touchStartX;
            const deltaY = touch.clientY - touchStartY;
            const minSwipeDistance = 30;
            
            if (Math.abs(deltaX) < minSwipeDistance && Math.abs(deltaY) < minSwipeDistance) {
                return; // 不是滑动手势
            }
            
            let moved = false;
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // 水平滑动
                if (deltaX > 0) {
                    moved = this.move('right');
                } else {
                    moved = this.move('left');
                }
            } else {
                // 垂直滑动
                if (deltaY > 0) {
                    moved = this.move('down');
                } else {
                    moved = this.move('up');
                }
            }
            
            if (moved) {
                this.audioManager.playSound('elementMove');
                this.afterMove();
            }
        });
    }

    setupUI() {
        // 重新开始按钮
        document.getElementById('restart-btn').addEventListener('click', () => {
            this.audioManager.playSound('buttonClick');
            this.restart();
        });
        
        // 帮助按钮
        document.getElementById('help-btn').addEventListener('click', () => {
            this.audioManager.playSound('buttonClick');
            this.showHelp();
        });
        
        // 模态框关闭
        document.getElementById('modal-close').addEventListener('click', () => {
            this.hideHelp();
        });
        
        // 覆盖层按钮
        document.getElementById('overlay-btn').addEventListener('click', () => {
            this.audioManager.playSound('buttonClick');
            this.restart();
        });
        
        // 点击模态框外部关闭
        document.getElementById('help-modal').addEventListener('click', (e) => {
            if (e.target.id === 'help-modal') {
                this.hideHelp();
            }
        });
    }

    initGrid() {
        this.grid = [];
        for (let i = 0; i < this.gridSize; i++) {
            this.grid[i] = [];
            for (let j = 0; j < this.gridSize; j++) {
                this.grid[i][j] = null;
            }
        }
    }

    // 保存当前网格状态
    saveGridState() {
        this.previousGrid = this.grid.map(row => row.slice());
    }

    // 获取空白位置
    getEmptyPositions() {
        const positions = [];
        for (let i = 0; i < this.gridSize; i++) {
            for (let j = 0; j < this.gridSize; j++) {
                if (this.grid[i][j] === null) {
                    positions.push({row: i, col: j});
                }
            }
        }
        return positions;
    }

    // 添加随机元素
    addRandomElement() {
        const emptyPositions = this.getEmptyPositions();
        if (emptyPositions.length === 0) return false;
        
        const randomPos = emptyPositions[Math.floor(Math.random() * emptyPositions.length)];
        const newElement = this.elementManager.createRandomElement();
        
        this.grid[randomPos.row][randomPos.col] = newElement;
        
        // 创建出现特效
        const cellX = randomPos.col * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
        const cellY = randomPos.row * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
        this.effectsManager.createElementAppearEffect(cellX, cellY, newElement.type);
        
        this.audioManager.playSound('elementAppear');
        return true;
    }

    // 移动逻辑
    move(direction) {
        this.saveGridState();
        let moved = false;
        let mergedPositions = [];
        
        const vectors = {
            up: {row: -1, col: 0},
            down: {row: 1, col: 0},
            left: {row: 0, col: -1},
            right: {row: 0, col: 1}
        };
        
        const vector = vectors[direction];
        const traversals = this.buildTraversals(vector);
        
        // 重置合并标记
        const merged = [];
        for (let i = 0; i < this.gridSize; i++) {
            merged[i] = [];
            for (let j = 0; j < this.gridSize; j++) {
                merged[i][j] = false;
            }
        }
        
        traversals.row.forEach(row => {
            traversals.col.forEach(col => {
                const element = this.grid[row][col];
                if (element) {
                    const positions = this.findFarthestPosition({row, col}, vector);
                    const next = this.grid[positions.next.row] && this.grid[positions.next.row][positions.next.col];
                    
                    // 检查是否可以合成
                    if (next && !merged[positions.next.row][positions.next.col] && element.canSynthesizeWith(next)) {
                        const synthesizedElement = element.synthesizeWith(next);
                        
                        // 执行合成
                        this.grid[positions.next.row][positions.next.col] = synthesizedElement;
                        this.grid[row][col] = null;
                        merged[positions.next.row][positions.next.col] = true;
                        
                        // 计算分数
                        const points = this.calculatePoints(synthesizedElement);
                        this.score += points;
                        
                        // 检查是否发现新元素
                        const isNewDiscovery = this.elementManager.discoverElement(synthesizedElement.type);
                        if (isNewDiscovery) {
                            this.visualFeedback.showElementDiscovery(
                                synthesizedElement.config.name,
                                synthesizedElement.config.symbol
                            );
                            this.audioManager.playSound('discovery');
                        }
                        
                        // 创建合成特效
                        const cellX = positions.next.col * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                        const cellY = positions.next.row * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                        this.effectsManager.createSynthesisEffect(cellX, cellY, element.type, synthesizedElement.type);
                        this.audioManager.playSound('elementMerge', synthesizedElement.type);
                        
                        mergedPositions.push(positions.next);
                        moved = true;
                        
                    } else if (positions.farthest.row !== row || positions.farthest.col !== col) {
                        // 移动到最远位置
                        this.grid[positions.farthest.row][positions.farthest.col] = element;
                        this.grid[row][col] = null;
                        moved = true;
                    }
                }
            });
        });
        
        return moved;
    }

    // 构建遍历顺序
    buildTraversals(vector) {
        const traversals = {row: [], col: []};

        for (let pos = 0; pos < this.gridSize; pos++) {
            traversals.row.push(pos);
            traversals.col.push(pos);
        }

        // 根据移动方向调整遍历顺序
        if (vector.row === 1) traversals.row = traversals.row.reverse();
        if (vector.col === 1) traversals.col = traversals.col.reverse();

        return traversals;
    }

    // 找到最远可移动位置
    findFarthestPosition(cell, vector) {
        let previous;

        do {
            previous = cell;
            cell = {
                row: previous.row + vector.row,
                col: previous.col + vector.col
            };
        } while (this.withinBounds(cell) && this.cellAvailable(cell));

        return {
            farthest: previous,
            next: cell
        };
    }

    // 检查位置是否在边界内
    withinBounds(position) {
        return position.row >= 0 && position.row < this.gridSize &&
               position.col >= 0 && position.col < this.gridSize;
    }

    // 检查单元格是否可用
    cellAvailable(cell) {
        return !this.cellOccupied(cell);
    }

    // 检查单元格是否被占用
    cellOccupied(cell) {
        return !!this.grid[cell.row][cell.col];
    }

    // 移动后处理
    afterMove() {
        if (!this.addRandomElement()) {
            // 无法添加新元素，检查游戏是否结束
            if (this.isGameOver()) {
                this.endGame();
                return;
            }
        }

        this.updateDisplay();
        this.updateChaosSystem();
        this.checkForEmergentEffects();
    }

    // 检查游戏是否结束
    isGameOver() {
        // 如果有空位，游戏继续
        if (this.getEmptyPositions().length > 0) {
            return false;
        }

        // 检查是否还有可能的移动
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (!element) continue;

                // 检查相邻位置是否可以合成
                const directions = [
                    {row: 0, col: 1},  // 右
                    {row: 1, col: 0}   // 下
                ];

                for (const dir of directions) {
                    const newRow = row + dir.row;
                    const newCol = col + dir.col;

                    if (newRow < this.gridSize && newCol < this.gridSize) {
                        const neighbor = this.grid[newRow][newCol];
                        if (neighbor && element.canSynthesizeWith(neighbor)) {
                            return false; // 还有可能的移动
                        }
                    }
                }
            }
        }

        return true; // 游戏结束
    }

    // 结束游戏
    endGame() {
        this.gameOver = true;
        this.isRunning = false;

        // 保存最高分
        if (this.score > this.bestScore) {
            this.bestScore = this.score;
            this.saveBestScore();
        }

        // 显示游戏结束界面
        this.showGameOverOverlay();
        this.audioManager.playSound('gameOver');

        // 创建游戏结束特效
        this.effectsManager.createChaosStormEffect(
            this.canvas.width / 2,
            this.canvas.height / 2,
            100
        );
    }

    // 计算分数
    calculatePoints(element) {
        const basePoints = element.value * element.config.level;
        const energyBonus = Math.floor(element.config.energy / 10);
        return basePoints + energyBonus;
    }

    // 更新混沌系统
    updateChaosSystem() {
        const currentTime = Date.now();

        // 增加混沌等级
        this.chaosLevel += 0.1;

        // 检查是否触发混沌事件
        if (currentTime - this.lastChaosTime > 10000 && Math.random() < this.chaosLevel / 100) {
            this.triggerChaosEvent();
            this.lastChaosTime = currentTime;
        }

        // 更新所有元素（检查衰变）
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element) {
                    const mutation = element.update();
                    if (mutation) {
                        this.grid[row][col] = mutation;
                        this.visualFeedback.showChaosEvent('元素变异', `${element.config.name} 变异为 ${mutation.config.name}`);

                        // 创建变异特效
                        const cellX = col * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                        const cellY = row * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                        this.effectsManager.createChaosStormEffect(cellX, cellY, 30);
                    }
                }
            }
        }
    }

    // 触发混沌事件
    triggerChaosEvent() {
        const events = [
            {
                name: '时空扭曲',
                description: '随机交换两个元素位置',
                effect: () => this.chaosSwapElements()
            },
            {
                name: '元素风暴',
                description: '所有元素获得能量加成',
                effect: () => this.chaosEnergyBoost()
            },
            {
                name: '重力反转',
                description: '元素向上移动',
                effect: () => this.chaosGravityReverse()
            },
            {
                name: '量子跃迁',
                description: '随机元素升级',
                effect: () => this.chaosQuantumLeap()
            }
        ];

        const randomEvent = events[Math.floor(Math.random() * events.length)];
        randomEvent.effect();

        this.visualFeedback.showChaosEvent(randomEvent.name, randomEvent.description);
        this.audioManager.playSound('chaosEvent');

        // 创建全屏混沌特效
        this.effectsManager.createChaosStormEffect(
            this.canvas.width / 2,
            this.canvas.height / 2,
            150
        );

        // 屏幕震动
        this.effectsManager.createScreenShake(1, 800);
    }

    // 混沌事件：交换元素
    chaosSwapElements() {
        const occupiedPositions = [];
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                if (this.grid[row][col]) {
                    occupiedPositions.push({row, col});
                }
            }
        }

        if (occupiedPositions.length >= 2) {
            const pos1 = occupiedPositions[Math.floor(Math.random() * occupiedPositions.length)];
            const pos2 = occupiedPositions[Math.floor(Math.random() * occupiedPositions.length)];

            if (pos1 !== pos2) {
                const temp = this.grid[pos1.row][pos1.col];
                this.grid[pos1.row][pos1.col] = this.grid[pos2.row][pos2.col];
                this.grid[pos2.row][pos2.col] = temp;
            }
        }
    }

    // 混沌事件：能量加成
    chaosEnergyBoost() {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element && Math.random() < 0.3) {
                    element.value = Math.min(element.value * 2, 2048);
                }
            }
        }
    }

    // 混沌事件：重力反转
    chaosGravityReverse() {
        this.move('up');
    }

    // 混沌事件：量子跃迁
    chaosQuantumLeap() {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element && Math.random() < 0.1) {
                    const mutation = element.mutate();
                    if (mutation) {
                        this.grid[row][col] = mutation;
                    }
                }
            }
        }
    }

    // 检查涌现效应
    checkForEmergentEffects() {
        // 检查连锁反应
        this.checkChainReactions();

        // 检查临界点
        this.checkCriticalPoints();
    }

    // 检查连锁反应
    checkChainReactions() {
        const elementCounts = {};
        const positions = {};

        // 统计元素数量和位置
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element) {
                    const type = element.type;
                    if (!elementCounts[type]) {
                        elementCounts[type] = 0;
                        positions[type] = [];
                    }
                    elementCounts[type]++;
                    positions[type].push({row, col});
                }
            }
        }

        // 检查是否触发连锁反应
        for (const [type, count] of Object.entries(elementCounts)) {
            if (count >= 3) {
                this.triggerChainReaction(type, positions[type]);
            }
        }
    }

    // 触发连锁反应
    triggerChainReaction(elementType, positions) {
        // 创建连锁反应特效
        const effectPositions = positions.map(pos => ({
            x: pos.col * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2,
            y: pos.row * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2
        }));

        this.effectsManager.createChainReactionEffect(effectPositions, elementType);
        this.audioManager.playSound('chainReaction', positions.length);

        // 给所有相同类型元素加分
        const bonusPoints = positions.length * 50;
        this.score += bonusPoints;

        this.visualFeedback.showChaosEvent('连锁反应', `${ElementConfig[elementType].name} 元素共鸣，获得 ${bonusPoints} 分！`);
    }

    // 检查临界点
    checkCriticalPoints() {
        let totalEnergy = 0;
        let highEnergyElements = 0;

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element) {
                    totalEnergy += element.config.energy;
                    if (element.config.energy > 100) {
                        highEnergyElements++;
                    }
                }
            }
        }

        // 临界点：总能量超过阈值
        if (totalEnergy > 800) {
            this.triggerCriticalPoint();
        }
    }

    // 触发临界点效应
    triggerCriticalPoint() {
        // 全局能量爆发
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element) {
                    // 创建光环特效
                    const cellX = col * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                    const cellY = row * (this.cellSize + this.cellPadding) + this.cellPadding + this.cellSize / 2;
                    this.effectsManager.createAuraEffect(cellX, cellY, element.type, 2);
                }
            }
        }

        // 分数加成
        const bonusPoints = Math.floor(this.score * 0.1);
        this.score += bonusPoints;

        this.visualFeedback.showChaosEvent('临界点', `能量临界！获得 ${bonusPoints} 分加成！`);
        this.effectsManager.createScreenShake(0.5, 500);
    }

    // 渲染游戏
    render() {
        // 清除画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景网格
        this.drawGrid();

        // 绘制元素
        this.drawElements();

        // 绘制混沌效果
        if (this.chaosLevel > 50) {
            this.drawChaosOverlay();
        }
    }

    // 绘制网格
    drawGrid() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.lineWidth = 1;

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const x = col * (this.cellSize + this.cellPadding) + this.cellPadding;
                const y = row * (this.cellSize + this.cellPadding) + this.cellPadding;

                this.ctx.fillRect(x, y, this.cellSize, this.cellSize);
                this.ctx.strokeRect(x, y, this.cellSize, this.cellSize);
            }
        }
    }

    // 绘制元素
    drawElements() {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element) {
                    this.drawElement(element, row, col);
                }
            }
        }
    }

    // 绘制单个元素
    drawElement(element, row, col) {
        const x = col * (this.cellSize + this.cellPadding) + this.cellPadding;
        const y = row * (this.cellSize + this.cellPadding) + this.cellPadding;
        const centerX = x + this.cellSize / 2;
        const centerY = y + this.cellSize / 2;

        // 绘制元素背景
        const gradient = this.ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, this.cellSize / 2);
        gradient.addColorStop(0, element.config.color);
        gradient.addColorStop(1, this.darkenColor(element.config.color, 0.3));

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(x, y, this.cellSize, this.cellSize);

        // 绘制元素边框
        this.ctx.strokeStyle = element.config.color;
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(x, y, this.cellSize, this.cellSize);

        // 绘制元素符号
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(element.config.symbol, centerX, centerY - 8);

        // 绘制元素数值
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillText(element.value.toString(), centerX, centerY + 15);

        // 绘制不稳定元素的特殊效果
        if (element.config.stability < 50) {
            this.drawInstabilityEffect(centerX, centerY, element.instability);
        }
    }

    // 绘制不稳定效果
    drawInstabilityEffect(x, y, instability) {
        const intensity = Math.min(instability / 10, 1);
        const radius = 5 + intensity * 10;

        this.ctx.save();
        this.ctx.globalAlpha = intensity * 0.5;
        this.ctx.strokeStyle = '#ff6b6b';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.stroke();
        this.ctx.restore();
    }

    // 绘制混沌覆盖层
    drawChaosOverlay() {
        const intensity = (this.chaosLevel - 50) / 50;
        this.ctx.save();
        this.ctx.globalAlpha = intensity * 0.1;
        this.ctx.fillStyle = '#ff6b6b';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();
    }

    // 颜色处理工具
    darkenColor(color, factor) {
        // 简单的颜色变暗函数
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        const newR = Math.floor(r * (1 - factor));
        const newG = Math.floor(g * (1 - factor));
        const newB = Math.floor(b * (1 - factor));

        return `rgb(${newR}, ${newG}, ${newB})`;
    }

    // 更新显示
    updateDisplay() {
        this.render();
        this.updateScore();
        this.updateElementInfo();
        this.updateSynthesisGuide();
    }

    // 更新分数显示
    updateScore() {
        document.getElementById('score').textContent = this.score.toLocaleString();
        document.getElementById('best-score').textContent = this.bestScore.toLocaleString();
    }

    // 更新元素信息面板
    updateElementInfo() {
        const currentElementDiv = document.getElementById('current-element');
        const preview = currentElementDiv.querySelector('.element-preview');
        const name = currentElementDiv.querySelector('.element-name');
        const temperature = document.getElementById('element-temperature');
        const stability = document.getElementById('element-stability');
        const energy = document.getElementById('element-energy');

        // 找到最高级别的元素作为当前显示
        let highestElement = null;
        let highestLevel = 0;

        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const element = this.grid[row][col];
                if (element && element.config.level > highestLevel) {
                    highestElement = element;
                    highestLevel = element.config.level;
                }
            }
        }

        if (highestElement) {
            preview.textContent = highestElement.config.symbol;
            preview.style.background = `linear-gradient(45deg, ${highestElement.config.color}, ${this.darkenColor(highestElement.config.color, 0.2)})`;
            name.textContent = highestElement.config.name;
            temperature.textContent = highestElement.config.temperature + '°';
            stability.textContent = highestElement.config.stability + '%';
            energy.textContent = highestElement.config.energy;
        } else {
            preview.textContent = '?';
            preview.style.background = 'linear-gradient(45deg, #333, #555)';
            name.textContent = '选择一个元素';
            temperature.textContent = '--';
            stability.textContent = '--';
            energy.textContent = '--';
        }
    }

    // 更新合成指南
    updateSynthesisGuide() {
        const synthesisList = document.getElementById('synthesis-list');
        const hints = this.elementManager.getSynthesisHints();

        synthesisList.innerHTML = '';
        hints.slice(0, 6).forEach(hint => {
            const item = document.createElement('div');
            item.className = 'synthesis-item';
            if (hint.discovered) {
                item.innerHTML = `<span class="synthesis-formula">${hint.formula}</span>`;
            } else {
                item.innerHTML = `<span class="synthesis-formula unknown">${hint.formula} (?)</span>`;
                item.style.opacity = '0.6';
            }
            synthesisList.appendChild(item);
        });
    }

    // 显示帮助
    showHelp() {
        document.getElementById('help-modal').style.display = 'flex';
    }

    // 隐藏帮助
    hideHelp() {
        document.getElementById('help-modal').style.display = 'none';
    }

    // 显示游戏结束覆盖层
    showGameOverOverlay() {
        const overlay = document.getElementById('game-overlay');
        const title = document.getElementById('overlay-title');
        const message = document.getElementById('overlay-message');

        title.textContent = '游戏结束';
        message.textContent = `最终分数: ${this.score.toLocaleString()}`;

        if (this.score > this.bestScore) {
            message.textContent += ' - 新纪录！';
        }

        overlay.style.display = 'flex';
    }

    // 隐藏游戏结束覆盖层
    hideGameOverOverlay() {
        document.getElementById('game-overlay').style.display = 'none';
    }

    // 重新开始游戏
    restart() {
        this.hideGameOverOverlay();
        this.score = 0;
        this.chaosLevel = 0;
        this.gameOver = false;
        this.isRunning = true;
        this.lastChaosTime = 0;

        this.initGrid();
        this.addRandomElement();
        this.addRandomElement();
        this.updateDisplay();

        // 清除所有特效
        this.effectsManager.clearAllEffects();

        // 重置事件日志
        const eventLog = document.getElementById('event-log');
        eventLog.innerHTML = '<div class="event-item">游戏重新开始...</div>';
    }

    // 保存最高分
    saveBestScore() {
        try {
            localStorage.setItem('alchemy2048-best-score', this.bestScore.toString());
        } catch (error) {
            console.warn('无法保存最高分:', error);
        }
    }

    // 加载最高分
    loadBestScore() {
        try {
            const saved = localStorage.getItem('alchemy2048-best-score');
            return saved ? parseInt(saved, 10) : 0;
        } catch (error) {
            console.warn('无法加载最高分:', error);
            return 0;
        }
    }

    // 获取游戏状态
    getGameState() {
        return {
            score: this.score,
            bestScore: this.bestScore,
            chaosLevel: this.chaosLevel,
            isRunning: this.isRunning,
            gameOver: this.gameOver,
            gridSize: this.gridSize,
            elementCount: this.getEmptyPositions().length,
            discoveredElements: this.elementManager.getDiscoveredElements().length
        };
    }

    // 暂停/恢复游戏
    togglePause() {
        if (this.gameOver) return;

        this.isPaused = !this.isPaused;
        this.isRunning = !this.isPaused;

        if (this.isPaused) {
            this.effectsManager.stop();
        } else {
            this.effectsManager.start();
        }
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待所有资源加载完成
    window.addEventListener('load', () => {
        try {
            window.game = new Game();
            console.log('游戏启动成功！');
        } catch (error) {
            console.error('游戏启动失败:', error);

            // 显示错误信息
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 9999;
                text-align: center;
            `;
            errorDiv.innerHTML = `
                <h3>游戏启动失败</h3>
                <p>请刷新页面重试</p>
                <p style="font-size: 0.8em; opacity: 0.8;">错误: ${error.message}</p>
            `;
            document.body.appendChild(errorDiv);
        }
    });
});

// 导出游戏类供调试使用
window.Game = Game;
