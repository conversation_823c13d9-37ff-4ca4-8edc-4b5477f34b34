// 元素炼金术2048 - 音效系统

class AudioManager {
    constructor() {
        this.audioContext = null;
        this.masterVolume = 0.7;
        this.sfxVolume = 0.8;
        this.musicVolume = 0.5;
        this.isEnabled = true;
        this.sounds = {};
        
        this.initAudioContext();
        this.createSounds();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建主音量控制
            this.masterGain = this.audioContext.createGain();
            this.masterGain.gain.value = this.masterVolume;
            this.masterGain.connect(this.audioContext.destination);
            
            // 创建音效和音乐的分离控制
            this.sfxGain = this.audioContext.createGain();
            this.sfxGain.gain.value = this.sfxVolume;
            this.sfxGain.connect(this.masterGain);
            
            this.musicGain = this.audioContext.createGain();
            this.musicGain.gain.value = this.musicVolume;
            this.musicGain.connect(this.masterGain);
            
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.isEnabled = false;
        }
    }

    createSounds() {
        if (!this.isEnabled) return;

        // 创建程序化音效
        this.sounds = {
            elementMove: this.createMoveSound(),
            elementMerge: this.createMergeSound(),
            elementAppear: this.createAppearSound(),
            chainReaction: this.createChainReactionSound(),
            chaosEvent: this.createChaosSound(),
            discovery: this.createDiscoverySound(),
            gameOver: this.createGameOverSound(),
            buttonClick: this.createButtonClickSound()
        };
    }

    // 创建移动音效
    createMoveSound() {
        return () => {
            if (!this.isEnabled) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(150, this.audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + 0.1);
        };
    }

    // 创建合成音效
    createMergeSound() {
        return (elementType) => {
            if (!this.isEnabled) return;
            
            const frequencies = {
                fire: [440, 550, 660],
                water: [220, 330, 440],
                earth: [110, 165, 220],
                air: [330, 440, 550],
                steam: [440, 660, 880],
                lava: [220, 440, 880],
                mud: [110, 220, 330],
                plasma: [550, 880, 1100]
            };
            
            const freqSet = frequencies[elementType] || frequencies.fire;
            
            freqSet.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.type = 'triangle';
                    oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                    
                    gainNode.gain.setValueAtTime(0.4, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.sfxGain);
                    
                    oscillator.start();
                    oscillator.stop(this.audioContext.currentTime + 0.3);
                }, index * 50);
            });
        };
    }

    // 创建元素出现音效
    createAppearSound() {
        return () => {
            if (!this.isEnabled) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = 'sawtooth';
            oscillator.frequency.setValueAtTime(100, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + 0.2);
        };
    }

    // 创建连锁反应音效
    createChainReactionSound() {
        return (chainLength) => {
            if (!this.isEnabled) return;
            
            for (let i = 0; i < chainLength; i++) {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.type = 'square';
                    oscillator.frequency.setValueAtTime(200 + i * 100, this.audioContext.currentTime);
                    
                    gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.15);
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.sfxGain);
                    
                    oscillator.start();
                    oscillator.stop(this.audioContext.currentTime + 0.15);
                }, i * 100);
            }
        };
    }

    // 创建混沌事件音效
    createChaosSound() {
        return () => {
            if (!this.isEnabled) return;
            
            // 创建噪音效果
            const bufferSize = this.audioContext.sampleRate * 0.5;
            const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < bufferSize; i++) {
                data[i] = (Math.random() - 0.5) * 0.3;
            }
            
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();
            
            source.buffer = buffer;
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(1000, this.audioContext.currentTime);
            filter.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.5);
            
            gainNode.gain.setValueAtTime(0.4, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
            
            source.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            source.start();
            source.stop(this.audioContext.currentTime + 0.5);
        };
    }

    // 创建发现新元素音效
    createDiscoverySound() {
        return () => {
            if (!this.isEnabled) return;
            
            const notes = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
            
            notes.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.type = 'sine';
                    oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                    
                    gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.4);
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.sfxGain);
                    
                    oscillator.start();
                    oscillator.stop(this.audioContext.currentTime + 0.4);
                }, index * 150);
            });
        };
    }

    // 创建游戏结束音效
    createGameOverSound() {
        return () => {
            if (!this.isEnabled) return;
            
            const notes = [440, 415.30, 392, 369.99, 349.23]; // A4 下降
            
            notes.forEach((freq, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();
                    
                    oscillator.type = 'triangle';
                    oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
                    
                    gainNode.gain.setValueAtTime(0.4, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.6);
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(this.sfxGain);
                    
                    oscillator.start();
                    oscillator.stop(this.audioContext.currentTime + 0.6);
                }, index * 200);
            });
        };
    }

    // 创建按钮点击音效
    createButtonClickSound() {
        return () => {
            if (!this.isEnabled) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + 0.1);
        };
    }

    // 播放音效
    playSound(soundName, ...args) {
        if (!this.isEnabled || !this.sounds[soundName]) return;
        
        try {
            // 确保音频上下文已启动
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            this.sounds[soundName](...args);
        } catch (error) {
            console.warn('Error playing sound:', error);
        }
    }

    // 设置音量
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.masterVolume;
        }
    }

    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        if (this.sfxGain) {
            this.sfxGain.gain.value = this.sfxVolume;
        }
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.musicGain) {
            this.musicGain.gain.value = this.musicVolume;
        }
    }

    // 启用/禁用音效
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled && this.audioContext) {
            this.audioContext.suspend();
        } else if (enabled && this.audioContext) {
            this.audioContext.resume();
        }
    }

    // 获取音频状态
    getStatus() {
        return {
            enabled: this.isEnabled,
            masterVolume: this.masterVolume,
            sfxVolume: this.sfxVolume,
            musicVolume: this.musicVolume,
            contextState: this.audioContext ? this.audioContext.state : 'unavailable'
        };
    }
}

// 导出供其他模块使用
window.AudioManager = AudioManager;
